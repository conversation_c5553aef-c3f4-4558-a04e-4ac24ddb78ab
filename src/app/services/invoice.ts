import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { CommonService } from './common';

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {
  private baseUrl = environment.baseUrl;
  // In-memory storage for invoices when API is not available
  private invoicesStore: any[] = [];

  constructor(
    private http: HttpClient,
    private commonService: CommonService
  ) {}

  // Get all invoices
  getInvoices(): Observable<any> {
    return this.http.get(`${this.baseUrl}get_invoices`).pipe(
      catchError(error => {
        console.error('Error fetching invoices from API:', error);
        // Return stored invoices if API fails
        return of({ invoices: this.invoicesStore });
      })
    );
  }

  // Create a new invoice
  createInvoice(invoiceData: any): Observable<any> {
    return this.http.post(`${this.baseUrl}create_invoice`, invoiceData).pipe(
      catchError(error => {
        console.error('Error creating invoice via API:', error);
        
        // Store invoice locally if API fails
        const newInvoice = {
          ...invoiceData,
          id: this.invoicesStore.length + 1
        };
        this.invoicesStore.unshift(newInvoice);
        
        return of({ success: true, invoice: newInvoice });
      })
    );
  }

  // Get a specific invoice by ID
  getInvoiceById(invoiceId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}get_invoice/${invoiceId}`).pipe(
      catchError(error => {
        console.error('Error fetching invoice by ID from API:', error);
        // Find in local store if API fails
        const invoice = this.invoicesStore.find(inv => inv.invoice_id === invoiceId);
        return of({ invoice });
      })
    );
  }
}